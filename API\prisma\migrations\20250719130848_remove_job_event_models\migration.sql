/*
  Warnings:

  - The values [JOB_POSTED,EVENT_CREATED] on the enum `notifications_type` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the `event_rsvps` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `events` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `job_applications` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `jobs` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE `event_rsvps` DROP FOREIGN KEY `event_rsvps_eventId_fkey`;

-- DropForeignKey
ALTER TABLE `event_rsvps` DROP FOREIGN KEY `event_rsvps_userId_fkey`;

-- DropForeignKey
ALTER TABLE `events` DROP FOREIGN KEY `events_organizerId_fkey`;

-- DropForeignKey
ALTER TABLE `job_applications` DROP FOREIGN KEY `job_applications_applicantId_fkey`;

-- DropForeignKey
ALTER TABLE `job_applications` DROP FOREIGN KEY `job_applications_jobId_fkey`;

-- DropForeignKey
ALTER TABLE `jobs` DROP FOREIGN KEY `jobs_postedById_fkey`;

-- AlterTable
ALTER TABLE `notifications` MODIFY `type` ENUM('MESSAGE_RECEIVED', 'CONNECTION_REQUEST', 'POST_CREATED', 'SYSTEM') NOT NULL;

-- DropTable
DROP TABLE `event_rsvps`;

-- DropTable
DROP TABLE `events`;

-- DropTable
DROP TABLE `job_applications`;

-- DropTable
DROP TABLE `jobs`;
