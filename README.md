# IonAlumni

This repository contains the complete codebase for the project, including the API, frontend, mobile application, and database schema.

## Projects Overview

- **API**: Node.js with Express.js for backend services.
- **Frontend**: Next.js for the web interface.
- **Mobile**: React Native app for iOS and Android.
- **Database**: MySQL schema and migration scripts.

## Structure

- `api/` - Backend API codebase.
- `frontend/` - Next.js frontend application.
- `mobile/` - React Native mobile application.
- `database/` - Database schema and migration scripts.

## Getting Started

### Prerequisites

- Node.js and npm/yarn installed.
- MySQL database setup.
- React Native development environment.

### Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/sushmitkumarpatil/IonAlumni.git
   cd IonAlumni
